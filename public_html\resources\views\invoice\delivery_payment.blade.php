{{ Form::open(['url' => route('invoice.delivery.payment.create', $invoice->id), 'method' => 'post']) }}
<div class="modal-body">
    <div class="row">
        <div class="col-md-12">
            <div class="alert alert-info">
                <h6 class="mb-2">
                    <i class="fas fa-truck"></i> {{ __('Delivery Payment') }}
                </h6>
                <p class="mb-1">
                    <strong>{{ __('Invoice') }}:</strong> {{ Auth::user()->invoiceNumberFormat($invoice->invoice_id) }}
                </p>
                <p class="mb-1">
                    <strong>{{ __('Customer') }}:</strong> {{ $invoice->customer->name ?? __('N/A') }}
                </p>
                <p class="mb-0">
                    <strong>{{ __('Amount Due') }}:</strong> 
                    <span class="text-danger">{{ Auth::user()->priceFormat($invoice->getDue()) }}</span>
                </p>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="form-group col-md-6">
            {{ Form::label('date', __('Date'), ['class' => 'form-label']) }}
            {{ Form::date('date', date('Y-m-d'), ['class' => 'form-control', 'required' => 'required']) }}
        </div>
        
        <div class="form-group col-md-6">
            {{ Form::label('amount', __('Amount'), ['class' => 'form-label']) }}
            {{ Form::number('amount', $invoice->getDue(), ['class' => 'form-control', 'required' => 'required', 'step' => '0.01', 'min' => '0.01', 'max' => $invoice->getDue()]) }}
        </div>
    </div>

    <div class="row">
        <div class="form-group col-md-6">
            {{ Form::label('account_id', __('Account'), ['class' => 'form-label']) }}
            {{ Form::select('account_id', $accounts, null, ['class' => 'form-control select2', 'required' => 'required', 'placeholder' => __('Select Account')]) }}
        </div>
        
        <div class="form-group col-md-6">
            {{ Form::label('payment_method', __('Payment Method'), ['class' => 'form-label']) }}
            {{ Form::select('payment_method', [
                'cash' => __('Cash'),
                'card' => __('Card'),
                'bank_transfer' => __('Bank Transfer'),
                'mobile_payment' => __('Mobile Payment')
            ], 'cash', ['class' => 'form-control select2', 'required' => 'required']) }}
        </div>
    </div>

    <div class="row">
        <div class="form-group col-md-6">
            {{ Form::label('reference', __('Reference'), ['class' => 'form-label']) }}
            {{ Form::text('reference', null, ['class' => 'form-control', 'placeholder' => __('Payment Reference')]) }}
        </div>
        
        <div class="form-group col-md-6">
            {{ Form::label('description', __('Description'), ['class' => 'form-label']) }}
            {{ Form::textarea('description', __('Payment collected by delivery'), ['class' => 'form-control', 'rows' => 3, 'placeholder' => __('Payment Description')]) }}
        </div>
    </div>

    <div class="row">
        <div class="col-md-12">
            <div class="alert alert-warning">
                <i class="fas fa-exclamation-triangle"></i>
                {{ __('Please ensure you have collected the payment before submitting this form.') }}
            </div>
        </div>
    </div>
</div>

<div class="modal-footer">
    <input type="button" value="{{ __('Cancel') }}" class="btn btn-secondary" data-bs-dismiss="modal">
    <input type="submit" value="{{ __('Add Payment') }}" class="btn btn-success">
</div>
{{ Form::close() }}

<script>
    $(document).ready(function() {
        $('.select2').select2({
            dropdownParent: $('.modal')
        });
    });
</script>
