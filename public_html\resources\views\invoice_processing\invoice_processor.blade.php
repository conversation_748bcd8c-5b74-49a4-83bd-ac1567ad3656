@extends('layouts.admin')
@section('page-title')
    {{__('معالج فواتير البيع')}}
@endsection
@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item"><a href="{{route('invoice.processing.index')}}">{{__('معالجة فواتير المبيعات')}}</a></li>
    <li class="breadcrumb-item">{{__('معالج فواتير البيع')}}</li>
@endsection
@push('css-page')
    <link rel="stylesheet" href="{{ asset('css/datatable/buttons.dataTables.min.css') }}">
@endpush

@section('content')
    <div id="printableArea">
        <div class="row mt-3">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>{{ __('معالج فواتير البيع') }}</h5>
                    </div>

                    <!-- فلاتر البحث -->
                    <div class="card-body border-bottom">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">
                                <i class="ti ti-filter"></i> {{ __('فلاتر البحث') }}
                            </h6>
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="toggleFilters">
                                <i class="ti ti-chevron-up"></i> {{ __('إخفاء/إظهار') }}
                            </button>
                        </div>

                        <div id="filtersContainer">
                            <form method="GET" action="{{ route('invoice.processing.invoice.processor') }}" id="filterForm">
                                <div class="row mb-3">
                                    <div class="col-md-3">
                                        <label for="date_from" class="form-label">
                                            <i class="ti ti-calendar"></i> {{ __('من تاريخ') }}
                                        </label>
                                        <input type="date" class="form-control" id="date_from" name="date_from"
                                               value="{{ request('date_from') }}" placeholder="{{ __('اختر التاريخ') }}">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="date_to" class="form-label">
                                            <i class="ti ti-calendar"></i> {{ __('إلى تاريخ') }}
                                        </label>
                                        <input type="date" class="form-control" id="date_to" name="date_to"
                                               value="{{ request('date_to') }}" placeholder="{{ __('اختر التاريخ') }}">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="warehouse_id" class="form-label">
                                            <i class="ti ti-building-warehouse"></i> {{ __('المستودع') }}
                                        </label>
                                        <select class="form-control" id="warehouse_id" name="warehouse_id">
                                            @foreach($warehouses as $id => $name)
                                                <option value="{{ $id }}" {{ request('warehouse_id') == $id ? 'selected' : '' }}>
                                                    {{ $name }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="payment_method" class="form-label">
                                            <i class="ti ti-credit-card"></i> {{ __('طريقة الدفع') }}
                                        </label>
                                        <select class="form-control" id="payment_method" name="payment_method">
                                            @foreach($paymentMethods as $value => $label)
                                                <option value="{{ $value }}" {{ request('payment_method') == $value ? 'selected' : '' }}>
                                                    {{ $label }}
                                                </option>
                                            @endforeach
                                        </select>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-12 d-flex justify-content-between align-items-center">
                                        <div>
                                            <button type="submit" class="btn btn-primary me-2">
                                                <i class="ti ti-search"></i> {{ __('بحث') }}
                                            </button>
                                            <a href="{{ route('invoice.processing.invoice.processor') }}" class="btn btn-secondary me-2">
                                                <i class="ti ti-refresh"></i> {{ __('إعادة تعيين') }}
                                            </a>
                                            <button type="button" class="btn btn-info" id="quickFilterToday">
                                                <i class="ti ti-calendar-today"></i> {{ __('اليوم') }}
                                            </button>
                                        </div>
                                        <div class="text-muted">
                                            <i class="ti ti-list-numbers"></i>
                                            {{ __('إجمالي النتائج') }}: <strong class="text-primary">{{ count($posPayments) }}</strong>
                                            @if(request()->hasAny(['date_from', 'date_to', 'warehouse_id', 'payment_method']))
                                                <span class="badge bg-success ms-2">{{ __('مفلتر') }}</span>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- إحصائيات سريعة -->
                    @if(isset($statistics))
                    <div class="card-body border-bottom">
                        <div class="row text-center">
                            <div class="col-md-2">
                                <div class="card bg-primary text-white">
                                    <div class="card-body p-2">
                                        <h6 class="mb-1">{{ $statistics['total_count'] }}</h6>
                                        <small>{{ __('إجمالي الفواتير') }}</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card bg-success text-white">
                                    <div class="card-body p-2">
                                        <h6 class="mb-1">{{ Auth::user()->priceFormat($statistics['total_amount']) }}</h6>
                                        <small>{{ __('إجمالي المبلغ') }}</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card bg-info text-white">
                                    <div class="card-body p-2">
                                        <h6 class="mb-1">{{ $statistics['cash_count'] }}</h6>
                                        <small>{{ __('دفع نقدي') }}</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card bg-warning text-white">
                                    <div class="card-body p-2">
                                        <h6 class="mb-1">{{ $statistics['network_count'] }}</h6>
                                        <small>{{ __('دفع شبكة') }}</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card bg-secondary text-white">
                                    <div class="card-body p-2">
                                        <h6 class="mb-1">{{ $statistics['delivery_count'] }}</h6>
                                        <small>{{ __('توصيل') }}</small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card bg-dark text-white">
                                    <div class="card-body p-2">
                                        <h6 class="mb-1 current-time">{{ date('H:i') }}</h6>
                                        <small>{{ __('الوقت الحالي') }}</small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endif

                    <div class="card-body table-border-style">
                        <div class="table-responsive">
                            <table class="table datatable">
                                <thead>
                                <tr>
                                    <th>{{__('رقم الفاتورة')}}</th>
                                    <th>{{ __('تاريخ') }}</th>
                                    <th>{{ __('عميل') }}</th>
                                    <th>{{ __('مستودع') }}</th>
                                    <th>{{ __('المجموع الفرعي') }}</th>
                                    <th>{{ __('الخصم') }}</th>
                                    <th>{{ __('المجموع') }}</th>
                                    <th>{{ __('طريقة الدفع') }}</th>
                                    <th>{{ __('منشئ الفاتورة') }}</th>
                                    <th>{{ __('المستخدم') }}</th>
                                    <th>{{ __('الإجراءات') }}</th>
                                </tr>
                                </thead>

                                <tbody>
                                @forelse ($posPayments as $pos)
                                    <tr>
                                        <td class="Id">
                                            <a href="{{ route('invoice.processing.show', $pos->id) }}" class="btn btn-outline-primary">
                                                {{ AUth::user()->posNumberFormat($pos->id) }}
                                            </a>
                                        </td>

                                        <td>{{ Auth::user()->dateFormat($pos->pos_date) }}</td>
                                        <td>{{ !empty($pos->customer) ? $pos->customer->name : __('Walk-in Customer') }}</td>
                                        <td>{{ !empty($pos->warehouse) ? $pos->warehouse->name : '' }}</td>
                                        <td>{{ Auth::user()->priceFormat($pos->getSubTotal()) }}</td>
                                        <td>{{ Auth::user()->priceFormat($pos->getTotalDiscount()) }}</td>
                                        <td>{{ Auth::user()->priceFormat($pos->getTotal()) }}</td>
                                        <td>
                                            @if($pos->status_type == 'returned')
                                                <span class="badge bg-danger text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('مرتجع بضاعة') }} ↩️</span>
                                            @elseif($pos->status_type == 'cancelled')
                                                <span class="badge bg-secondary text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('ملغية') }} ❌</span>
                                            @elseif(!empty($pos->customer) && $pos->customer->is_delivery)
                                                @if($pos->is_payment_set)
                                                    <span class="badge bg-success text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('تم التحصيل من مندوب التوصيل') }} ✅</span>
                                                @else
                                                    <span class="badge bg-warning text-dark" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('جاري التحصيل') }} 🚚</span>
                                                @endif
                                            @elseif(isset($pos->posPayment) && $pos->posPayment->payment_type)
                                                @if($pos->posPayment->payment_type == 'cash')
                                                    <span class="badge bg-success text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('نقد') }} 💵</span>
                                                @elseif($pos->posPayment->payment_type == 'network')
                                                    <span class="badge bg-info text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('شبكة') }} 💳</span>
                                                @elseif($pos->posPayment->payment_type == 'split')
                                                    <span class="badge bg-warning text-dark" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('دفع مقسم') }} 💳 💵</span>
                                                @else
                                                    <span class="badge bg-danger text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('غير مدفوع') }}</span>
                                                @endif
                                            @else
                                                <span class="badge bg-danger text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('غير مدفوع') }}</span>
                                            @endif
                                        </td>
                                        <td>
                                            @if(!empty($pos->createdBy))
                                                @if($pos->createdBy->type == 'company')
                                                    @if(!empty($pos->user_id) && !empty(\App\Models\User::find($pos->user_id)))
                                                        {{ \App\Models\User::find($pos->user_id)->name }}
                                                    @elseif(!empty($pos->shift) && !empty($pos->shift->creator))
                                                        {{ $pos->shift->creator->name }}
                                                    @else
                                                        {{ $pos->createdBy->name }}
                                                    @endif
                                                @else
                                                    {{ $pos->createdBy->name }}
                                                @endif
                                            @else
                                                {{ __('غير معروف') }}
                                            @endif
                                        </td>
                                        <td>
                                            @if(!empty($pos->user))
                                                {{ $pos->user->name }}
                                            @elseif(!empty($pos->user_id) && !empty(\App\Models\User::find($pos->user_id)))
                                                {{ \App\Models\User::find($pos->user_id)->name }}
                                            @elseif(!empty($pos->shift) && !empty($pos->shift->creator))
                                                {{ $pos->shift->creator->name }}
                                            @else
                                                {{ __('غير معروف') }}
                                            @endif
                                        </td>
                                        <td class="Action">
                                            <div class="action-btn bg-primary ms-2">
                                                <a href="{{ route('invoice.processing.edit.products', $pos->id) }}" class="mx-3 btn btn-sm d-inline-flex align-items-center" data-bs-toggle="tooltip" title="{{ __('تعديل المنتجات') }}">
                                                    <i class="ti ti-edit text-white"></i>
                                                </a>
                                            </div>
                                            <div class="action-btn bg-danger ms-2">
                                                <a href="{{ route('invoice.processing.return', $pos->id) }}" class="mx-3 btn btn-sm d-inline-flex align-items-center" data-bs-toggle="tooltip" title="{{ __('مرتجع') }}" onclick="return confirm('{{ __('هل أنت متأكد من تغيير حالة الفاتورة إلى مرتجع بضاعة؟') }}')">
                                                    <i class="ti ti-arrow-back-up text-white"></i>
                                                </a>
                                            </div>
                                            <div class="action-btn bg-warning ms-2">
                                                <a href="{{ route('invoice.processing.cancel', $pos->id) }}" class="mx-3 btn btn-sm d-inline-flex align-items-center" data-bs-toggle="tooltip" title="{{ __('كنسل') }}" onclick="return confirm('{{ __('هل أنت متأكد من تغيير حالة الفاتورة إلى ملغية؟') }}')">
                                                    <i class="ti ti-x text-white"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                @empty
                                    <tr>
                                        <td colspan="11" class="text-center">{{ __('لا توجد فواتير متاحة') }}</td>
                                    </tr>
                                @endforelse
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@push('script-page')
<script>
    $(document).ready(function() {
        // إخفاء/إظهار الفلاتر
        $('#toggleFilters').on('click', function() {
            $('#filtersContainer').slideToggle();
            var icon = $(this).find('i');
            if (icon.hasClass('ti-chevron-up')) {
                icon.removeClass('ti-chevron-up').addClass('ti-chevron-down');
            } else {
                icon.removeClass('ti-chevron-down').addClass('ti-chevron-up');
            }
        });

        // فلتر سريع لليوم الحالي
        $('#quickFilterToday').on('click', function() {
            var today = new Date().toISOString().split('T')[0];
            $('#date_from').val(today);
            $('#date_to').val(today);
            $('#filterForm').submit();
        });

        // تطبيق الفلترة التلقائية عند تغيير القيم
        $('#warehouse_id, #payment_method').on('change', function() {
            $('#filterForm').submit();
        });

        // تطبيق فلتر التاريخ عند الضغط على Enter أو فقدان التركيز
        $('#date_from, #date_to').on('change blur', function() {
            var dateFrom = $('#date_from').val();
            var dateTo = $('#date_to').val();

            // التحقق من صحة التواريخ
            if (dateFrom && dateTo && dateFrom > dateTo) {
                alert('{{ __("تاريخ البداية يجب أن يكون أقل من أو يساوي تاريخ النهاية") }}');
                $(this).val('');
                return;
            }

            // تطبيق الفلتر إذا كان كلا التاريخين محددين
            if (dateFrom && dateTo) {
                $('#filterForm').submit();
            }
        });

        // تحسين تجربة المستخدم - إضافة تأثيرات بصرية
        $('.form-control').on('focus', function() {
            $(this).addClass('border-primary shadow-sm');
        }).on('blur', function() {
            $(this).removeClass('border-primary shadow-sm');
        });

        // إضافة تأثير تحميل عند الإرسال
        $('#filterForm').on('submit', function() {
            var submitBtn = $(this).find('button[type="submit"]');
            var originalText = submitBtn.html();
            submitBtn.html('<i class="ti ti-loader-2 fa-spin"></i> {{ __("جاري البحث...") }}');
            submitBtn.prop('disabled', true);

            // إعادة تعيين النص بعد ثانيتين في حالة عدم تحميل الصفحة
            setTimeout(function() {
                submitBtn.html(originalText);
                submitBtn.prop('disabled', false);
            }, 2000);
        });

        // إضافة بحث سريع في الجدول
        if ($('.datatable').length) {
            var table = $('.datatable').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
                },
                "order": [[ 0, "desc" ]],
                "pageLength": 25,
                "responsive": true,
                "dom": 'Bfrtip',
                "buttons": [
                    {
                        extend: 'excel',
                        text: '<i class="ti ti-file-spreadsheet"></i> {{ __("تصدير Excel") }}',
                        className: 'btn btn-success btn-sm me-1'
                    },
                    {
                        extend: 'pdf',
                        text: '<i class="ti ti-file-text"></i> {{ __("تصدير PDF") }}',
                        className: 'btn btn-danger btn-sm me-1'
                    },
                    {
                        extend: 'print',
                        text: '<i class="ti ti-printer"></i> {{ __("طباعة") }}',
                        className: 'btn btn-info btn-sm'
                    }
                ],
                "initComplete": function() {
                    // إضافة بحث مخصص لكل عمود
                    this.api().columns().every(function() {
                        var column = this;
                        if (column.index() < 8) { // فقط للأعمدة الأولى
                            var select = $('<select class="form-control form-control-sm"><option value="">{{ __("الكل") }}</option></select>')
                                .appendTo($(column.footer()).empty())
                                .on('change', function() {
                                    var val = $.fn.dataTable.util.escapeRegex(
                                        $(this).val()
                                    );
                                    column
                                        .search(val ? '^' + val + '$' : '', true, false)
                                        .draw();
                                });

                            column.data().unique().sort().each(function(d, j) {
                                if (d) {
                                    select.append('<option value="' + d + '">' + d + '</option>');
                                }
                            });
                        }
                    });
                }
            });
        }

        // إضافة tooltips للأزرار
        $('[data-bs-toggle="tooltip"]').tooltip();

        // تحديث الوقت كل دقيقة
        setInterval(function() {
            $('.current-time').text(new Date().toLocaleTimeString('ar-SA'));
        }, 60000);
    });
</script>
@endpush
