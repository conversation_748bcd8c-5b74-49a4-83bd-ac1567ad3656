<?php $__env->startSection('page-title'); ?>
    <?php echo e(__('معالج فواتير البيع')); ?>

<?php $__env->stopSection(); ?>
<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><a href="<?php echo e(route('dashboard')); ?>"><?php echo e(__('Dashboard')); ?></a></li>
    <li class="breadcrumb-item"><a href="<?php echo e(route('invoice.processing.index')); ?>"><?php echo e(__('معالجة فواتير المبيعات')); ?></a></li>
    <li class="breadcrumb-item"><?php echo e(__('معالج فواتير البيع')); ?></li>
<?php $__env->stopSection(); ?>
<?php $__env->startPush('css-page'); ?>
    <link rel="stylesheet" href="<?php echo e(asset('css/datatable/buttons.dataTables.min.css')); ?>">
<?php $__env->stopPush(); ?>

<?php $__env->startSection('content'); ?>
    <div id="printableArea">
        <div class="row mt-3">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5><?php echo e(__('معالج فواتير البيع')); ?></h5>
                    </div>

                    <!-- فلاتر البحث -->
                    <div class="card-body border-bottom">
                        <div class="d-flex justify-content-between align-items-center mb-3">
                            <h6 class="mb-0">
                                <i class="ti ti-filter"></i> <?php echo e(__('فلاتر البحث')); ?>

                            </h6>
                            <button type="button" class="btn btn-sm btn-outline-secondary" id="toggleFilters">
                                <i class="ti ti-chevron-up"></i> <?php echo e(__('إخفاء/إظهار')); ?>

                            </button>
                        </div>

                        <div id="filtersContainer">
                            <form method="GET" action="<?php echo e(route('invoice.processing.invoice.processor')); ?>" id="filterForm">
                                <div class="row mb-3">
                                    <div class="col-md-3">
                                        <label for="date_from" class="form-label">
                                            <i class="ti ti-calendar"></i> <?php echo e(__('من تاريخ')); ?>

                                        </label>
                                        <input type="date" class="form-control" id="date_from" name="date_from"
                                               value="<?php echo e(request('date_from')); ?>" placeholder="<?php echo e(__('اختر التاريخ')); ?>">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="date_to" class="form-label">
                                            <i class="ti ti-calendar"></i> <?php echo e(__('إلى تاريخ')); ?>

                                        </label>
                                        <input type="date" class="form-control" id="date_to" name="date_to"
                                               value="<?php echo e(request('date_to')); ?>" placeholder="<?php echo e(__('اختر التاريخ')); ?>">
                                    </div>
                                    <div class="col-md-3">
                                        <label for="warehouse_id" class="form-label">
                                            <i class="ti ti-building-warehouse"></i> <?php echo e(__('المستودع')); ?>

                                        </label>
                                        <select class="form-control" id="warehouse_id" name="warehouse_id">
                                            <?php $__currentLoopData = $warehouses; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $id => $name): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($id); ?>" <?php echo e(request('warehouse_id') == $id ? 'selected' : ''); ?>>
                                                    <?php echo e($name); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="payment_method" class="form-label">
                                            <i class="ti ti-credit-card"></i> <?php echo e(__('طريقة الدفع')); ?>

                                        </label>
                                        <select class="form-control" id="payment_method" name="payment_method">
                                            <?php $__currentLoopData = $paymentMethods; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $value => $label): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                                <option value="<?php echo e($value); ?>" <?php echo e(request('payment_method') == $value ? 'selected' : ''); ?>>
                                                    <?php echo e($label); ?>

                                                </option>
                                            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                        </select>
                                    </div>
                                </div>
                                <div class="row mb-3">
                                    <div class="col-md-12 d-flex justify-content-between align-items-center">
                                        <div>
                                            <button type="submit" class="btn btn-primary me-2">
                                                <i class="ti ti-search"></i> <?php echo e(__('بحث')); ?>

                                            </button>
                                            <a href="<?php echo e(route('invoice.processing.invoice.processor')); ?>" class="btn btn-secondary me-2">
                                                <i class="ti ti-refresh"></i> <?php echo e(__('إعادة تعيين')); ?>

                                            </a>
                                            <button type="button" class="btn btn-info" id="quickFilterToday">
                                                <i class="ti ti-calendar-today"></i> <?php echo e(__('اليوم')); ?>

                                            </button>
                                        </div>
                                        <div class="text-muted">
                                            <i class="ti ti-list-numbers"></i>
                                            <?php echo e(__('إجمالي النتائج')); ?>: <strong class="text-primary"><?php echo e(count($posPayments)); ?></strong>
                                            <?php if(request()->hasAny(['date_from', 'date_to', 'warehouse_id', 'payment_method'])): ?>
                                                <span class="badge bg-success ms-2"><?php echo e(__('مفلتر')); ?></span>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- إحصائيات سريعة -->
                    <?php if(isset($statistics)): ?>
                    <div class="card-body border-bottom">
                        <div class="row text-center">
                            <div class="col-md-2">
                                <div class="card bg-primary text-white">
                                    <div class="card-body p-2">
                                        <h6 class="mb-1"><?php echo e($statistics['total_count']); ?></h6>
                                        <small><?php echo e(__('إجمالي الفواتير')); ?></small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card bg-success text-white">
                                    <div class="card-body p-2">
                                        <h6 class="mb-1"><?php echo e(Auth::user()->priceFormat($statistics['total_amount'])); ?></h6>
                                        <small><?php echo e(__('إجمالي المبلغ')); ?></small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card bg-info text-white">
                                    <div class="card-body p-2">
                                        <h6 class="mb-1"><?php echo e($statistics['cash_count']); ?></h6>
                                        <small><?php echo e(__('دفع نقدي')); ?></small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card bg-warning text-white">
                                    <div class="card-body p-2">
                                        <h6 class="mb-1"><?php echo e($statistics['network_count']); ?></h6>
                                        <small><?php echo e(__('دفع شبكة')); ?></small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card bg-secondary text-white">
                                    <div class="card-body p-2">
                                        <h6 class="mb-1"><?php echo e($statistics['delivery_count']); ?></h6>
                                        <small><?php echo e(__('توصيل')); ?></small>
                                    </div>
                                </div>
                            </div>
                            <div class="col-md-2">
                                <div class="card bg-dark text-white">
                                    <div class="card-body p-2">
                                        <h6 class="mb-1 current-time"><?php echo e(date('H:i')); ?></h6>
                                        <small><?php echo e(__('الوقت الحالي')); ?></small>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <?php endif; ?>

                    <div class="card-body table-border-style">
                        <div class="table-responsive">
                            <table class="table datatable">
                                <thead>
                                <tr>
                                    <th><?php echo e(__('رقم الفاتورة')); ?></th>
                                    <th><?php echo e(__('تاريخ')); ?></th>
                                    <th><?php echo e(__('عميل')); ?></th>
                                    <th><?php echo e(__('مستودع')); ?></th>
                                    <th><?php echo e(__('المجموع الفرعي')); ?></th>
                                    <th><?php echo e(__('الخصم')); ?></th>
                                    <th><?php echo e(__('المجموع')); ?></th>
                                    <th><?php echo e(__('طريقة الدفع')); ?></th>
                                    <th><?php echo e(__('منشئ الفاتورة')); ?></th>
                                    <th><?php echo e(__('المستخدم')); ?></th>
                                    <th><?php echo e(__('الإجراءات')); ?></th>
                                </tr>
                                </thead>

                                <tbody>
                                <?php $__empty_1 = true; $__currentLoopData = $posPayments; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $pos): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                                    <tr>
                                        <td class="Id">
                                            <a href="<?php echo e(route('invoice.processing.show', $pos->id)); ?>" class="btn btn-outline-primary">
                                                <?php echo e(AUth::user()->posNumberFormat($pos->id)); ?>

                                            </a>
                                        </td>

                                        <td><?php echo e(Auth::user()->dateFormat($pos->pos_date)); ?></td>
                                        <td><?php echo e(!empty($pos->customer) ? $pos->customer->name : __('Walk-in Customer')); ?></td>
                                        <td><?php echo e(!empty($pos->warehouse) ? $pos->warehouse->name : ''); ?></td>
                                        <td><?php echo e(Auth::user()->priceFormat($pos->getSubTotal())); ?></td>
                                        <td><?php echo e(Auth::user()->priceFormat($pos->getTotalDiscount())); ?></td>
                                        <td><?php echo e(Auth::user()->priceFormat($pos->getTotal())); ?></td>
                                        <td>
                                            <?php if($pos->status_type == 'returned'): ?>
                                                <span class="badge bg-danger text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('مرتجع بضاعة')); ?> ↩️</span>
                                            <?php elseif($pos->status_type == 'cancelled'): ?>
                                                <span class="badge bg-secondary text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('ملغية')); ?> ❌</span>
                                            <?php elseif(!empty($pos->customer) && $pos->customer->is_delivery): ?>
                                                <?php if($pos->is_payment_set): ?>
                                                    <span class="badge bg-success text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('تم التحصيل من مندوب التوصيل')); ?> ✅</span>
                                                <?php else: ?>
                                                    <span class="badge bg-warning text-dark" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('جاري التحصيل')); ?> 🚚</span>
                                                <?php endif; ?>
                                            <?php elseif(isset($pos->posPayment) && $pos->posPayment->payment_type): ?>
                                                <?php if($pos->posPayment->payment_type == 'cash'): ?>
                                                    <span class="badge bg-success text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('نقد')); ?> 💵</span>
                                                <?php elseif($pos->posPayment->payment_type == 'network'): ?>
                                                    <span class="badge bg-info text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('شبكة')); ?> 💳</span>
                                                <?php elseif($pos->posPayment->payment_type == 'split'): ?>
                                                    <span class="badge bg-warning text-dark" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('دفع مقسم')); ?> 💳 💵</span>
                                                <?php else: ?>
                                                    <span class="badge bg-danger text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('غير مدفوع')); ?></span>
                                                <?php endif; ?>
                                            <?php else: ?>
                                                <span class="badge bg-danger text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;"><?php echo e(__('غير مدفوع')); ?></span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if(!empty($pos->createdBy)): ?>
                                                <?php if($pos->createdBy->type == 'company'): ?>
                                                    <?php if(!empty($pos->user_id) && !empty(\App\Models\User::find($pos->user_id))): ?>
                                                        <?php echo e(\App\Models\User::find($pos->user_id)->name); ?>

                                                    <?php elseif(!empty($pos->shift) && !empty($pos->shift->creator)): ?>
                                                        <?php echo e($pos->shift->creator->name); ?>

                                                    <?php else: ?>
                                                        <?php echo e($pos->createdBy->name); ?>

                                                    <?php endif; ?>
                                                <?php else: ?>
                                                    <?php echo e($pos->createdBy->name); ?>

                                                <?php endif; ?>
                                            <?php else: ?>
                                                <?php echo e(__('غير معروف')); ?>

                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php if(!empty($pos->user)): ?>
                                                <?php echo e($pos->user->name); ?>

                                            <?php elseif(!empty($pos->user_id) && !empty(\App\Models\User::find($pos->user_id))): ?>
                                                <?php echo e(\App\Models\User::find($pos->user_id)->name); ?>

                                            <?php elseif(!empty($pos->shift) && !empty($pos->shift->creator)): ?>
                                                <?php echo e($pos->shift->creator->name); ?>

                                            <?php else: ?>
                                                <?php echo e(__('غير معروف')); ?>

                                            <?php endif; ?>
                                        </td>
                                        <td class="Action">
                                            <div class="action-btn bg-primary ms-2">
                                                <a href="<?php echo e(route('invoice.processing.edit.products', $pos->id)); ?>" class="mx-3 btn btn-sm d-inline-flex align-items-center" data-bs-toggle="tooltip" title="<?php echo e(__('تعديل المنتجات')); ?>">
                                                    <i class="ti ti-edit text-white"></i>
                                                </a>
                                            </div>
                                            <div class="action-btn bg-danger ms-2">
                                                <a href="<?php echo e(route('invoice.processing.return', $pos->id)); ?>" class="mx-3 btn btn-sm d-inline-flex align-items-center" data-bs-toggle="tooltip" title="<?php echo e(__('مرتجع')); ?>" onclick="return confirm('<?php echo e(__('هل أنت متأكد من تغيير حالة الفاتورة إلى مرتجع بضاعة؟')); ?>')">
                                                    <i class="ti ti-arrow-back-up text-white"></i>
                                                </a>
                                            </div>
                                            <div class="action-btn bg-warning ms-2">
                                                <a href="<?php echo e(route('invoice.processing.cancel', $pos->id)); ?>" class="mx-3 btn btn-sm d-inline-flex align-items-center" data-bs-toggle="tooltip" title="<?php echo e(__('كنسل')); ?>" onclick="return confirm('<?php echo e(__('هل أنت متأكد من تغيير حالة الفاتورة إلى ملغية؟')); ?>')">
                                                    <i class="ti ti-x text-white"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                                    <tr>
                                        <td colspan="11" class="text-center"><?php echo e(__('لا توجد فواتير متاحة')); ?></td>
                                    </tr>
                                <?php endif; ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startPush('script-page'); ?>
<script>
    $(document).ready(function() {
        // إخفاء/إظهار الفلاتر
        $('#toggleFilters').on('click', function() {
            $('#filtersContainer').slideToggle();
            var icon = $(this).find('i');
            if (icon.hasClass('ti-chevron-up')) {
                icon.removeClass('ti-chevron-up').addClass('ti-chevron-down');
            } else {
                icon.removeClass('ti-chevron-down').addClass('ti-chevron-up');
            }
        });

        // فلتر سريع لليوم الحالي
        $('#quickFilterToday').on('click', function() {
            var today = new Date().toISOString().split('T')[0];
            $('#date_from').val(today);
            $('#date_to').val(today);
            $('#filterForm').submit();
        });

        // تطبيق الفلترة التلقائية عند تغيير القيم
        $('#warehouse_id, #payment_method').on('change', function() {
            $('#filterForm').submit();
        });

        // تطبيق فلتر التاريخ عند الضغط على Enter أو فقدان التركيز
        $('#date_from, #date_to').on('change blur', function() {
            var dateFrom = $('#date_from').val();
            var dateTo = $('#date_to').val();

            // التحقق من صحة التواريخ
            if (dateFrom && dateTo && dateFrom > dateTo) {
                alert('<?php echo e(__("تاريخ البداية يجب أن يكون أقل من أو يساوي تاريخ النهاية")); ?>');
                $(this).val('');
                return;
            }

            // تطبيق الفلتر إذا كان كلا التاريخين محددين
            if (dateFrom && dateTo) {
                $('#filterForm').submit();
            }
        });

        // تحسين تجربة المستخدم - إضافة تأثيرات بصرية
        $('.form-control').on('focus', function() {
            $(this).addClass('border-primary shadow-sm');
        }).on('blur', function() {
            $(this).removeClass('border-primary shadow-sm');
        });

        // إضافة تأثير تحميل عند الإرسال
        $('#filterForm').on('submit', function() {
            var submitBtn = $(this).find('button[type="submit"]');
            var originalText = submitBtn.html();
            submitBtn.html('<i class="ti ti-loader-2 fa-spin"></i> <?php echo e(__("جاري البحث...")); ?>');
            submitBtn.prop('disabled', true);

            // إعادة تعيين النص بعد ثانيتين في حالة عدم تحميل الصفحة
            setTimeout(function() {
                submitBtn.html(originalText);
                submitBtn.prop('disabled', false);
            }, 2000);
        });

        // إضافة بحث سريع في الجدول
        if ($('.datatable').length) {
            var table = $('.datatable').DataTable({
                "language": {
                    "url": "//cdn.datatables.net/plug-ins/1.10.25/i18n/Arabic.json"
                },
                "order": [[ 0, "desc" ]],
                "pageLength": 25,
                "responsive": true,
                "dom": 'Bfrtip',
                "buttons": [
                    {
                        extend: 'excel',
                        text: '<i class="ti ti-file-spreadsheet"></i> <?php echo e(__("تصدير Excel")); ?>',
                        className: 'btn btn-success btn-sm me-1'
                    },
                    {
                        extend: 'pdf',
                        text: '<i class="ti ti-file-text"></i> <?php echo e(__("تصدير PDF")); ?>',
                        className: 'btn btn-danger btn-sm me-1'
                    },
                    {
                        extend: 'print',
                        text: '<i class="ti ti-printer"></i> <?php echo e(__("طباعة")); ?>',
                        className: 'btn btn-info btn-sm'
                    }
                ],
                "initComplete": function() {
                    // إضافة بحث مخصص لكل عمود
                    this.api().columns().every(function() {
                        var column = this;
                        if (column.index() < 8) { // فقط للأعمدة الأولى
                            var select = $('<select class="form-control form-control-sm"><option value=""><?php echo e(__("الكل")); ?></option></select>')
                                .appendTo($(column.footer()).empty())
                                .on('change', function() {
                                    var val = $.fn.dataTable.util.escapeRegex(
                                        $(this).val()
                                    );
                                    column
                                        .search(val ? '^' + val + '$' : '', true, false)
                                        .draw();
                                });

                            column.data().unique().sort().each(function(d, j) {
                                if (d) {
                                    select.append('<option value="' + d + '">' + d + '</option>');
                                }
                            });
                        }
                    });
                }
            });
        }

        // إضافة tooltips للأزرار
        $('[data-bs-toggle="tooltip"]').tooltip();

        // تحديث الوقت كل دقيقة
        setInterval(function() {
            $('.current-time').text(new Date().toLocaleTimeString('ar-SA'));
        }, 60000);
    });
</script>
<?php $__env->stopPush(); ?>

<?php echo $__env->make('layouts.admin', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH C:\laragon\www\ERPcopy\public_html\resources\views/invoice_processing/invoice_processor.blade.php ENDPATH**/ ?>