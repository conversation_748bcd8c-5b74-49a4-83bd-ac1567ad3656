@extends('layouts.admin')
@section('page-title')
    {{ __('ملخص فواتير نقاط البيع - Delivery') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item">{{__('ملخص فواتير نقاط البيع - Delivery')}}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        <a href="#" class="btn btn-sm btn-primary" onclick="javascript:window.print()">
            <span class="btn-inner--icon"><i class="ti ti-printer"></i></span>
            {{__('Print')}}
        </a>
    </div>
@endsection

@section('content')
    <div id="printableArea">
        {{-- إحصائيات سريعة للـ Delivery --}}
        <div class="row mb-4">
            @php
                $totalDeliveryOrders = $posPayments->where('customer.is_delivery', true)->count();
                $pendingDeliveryOrders = $posPayments->where('customer.is_delivery', true)->where('is_payment_set', false)->count();
                $completedDeliveryOrders = $posPayments->where('customer.is_delivery', true)->where('is_payment_set', true)->count();
                $totalDeliveryAmount = $posPayments->where('customer.is_delivery', true)->sum(function($pos) { return $pos->getTotal(); });
                $pendingDeliveryAmount = $posPayments->where('customer.is_delivery', true)->where('is_payment_set', false)->sum(function($pos) { return $pos->getTotal(); });
            @endphp
            
            <div class="col-lg-3 col-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center justify-content-between">
                            <div class="col-auto mb-3 mb-sm-0">
                                <div class="d-flex align-items-center">
                                    <div class="theme-avtar bg-primary">
                                        <i class="ti ti-truck-delivery"></i>
                                    </div>
                                    <div class="ms-3">
                                        <small class="text-muted">{{__('إجمالي طلبات التوصيل')}}</small>
                                        <h6 class="m-0">{{ $totalDeliveryOrders }}</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center justify-content-between">
                            <div class="col-auto mb-3 mb-sm-0">
                                <div class="d-flex align-items-center">
                                    <div class="theme-avtar bg-warning">
                                        <i class="ti ti-clock"></i>
                                    </div>
                                    <div class="ms-3">
                                        <small class="text-muted">{{__('جاري التحصيل')}}</small>
                                        <h6 class="m-0">{{ $pendingDeliveryOrders }}</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center justify-content-between">
                            <div class="col-auto mb-3 mb-sm-0">
                                <div class="d-flex align-items-center">
                                    <div class="theme-avtar bg-success">
                                        <i class="ti ti-check"></i>
                                    </div>
                                    <div class="ms-3">
                                        <small class="text-muted">{{__('تم التحصيل')}}</small>
                                        <h6 class="m-0">{{ $completedDeliveryOrders }}</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="col-lg-3 col-6">
                <div class="card">
                    <div class="card-body">
                        <div class="row align-items-center justify-content-between">
                            <div class="col-auto mb-3 mb-sm-0">
                                <div class="d-flex align-items-center">
                                    <div class="theme-avtar bg-info">
                                        <i class="ti ti-currency-dollar"></i>
                                    </div>
                                    <div class="ms-3">
                                        <small class="text-muted">{{__('إجمالي المبلغ')}}</small>
                                        <h6 class="m-0">{{ Auth::user()->priceFormat($totalDeliveryAmount) }}</h6>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="row mt-3">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header">
                        <h5>{{ __('ملخص فواتير نقاط البيع - Delivery') }}</h5>
                        <small class="text-muted">{{ __('عرض جميع الفواتير مع التركيز على طلبات التوصيل') }}</small>
                    </div>
                    <div class="card-body table-border-style">
                        <div class="table-responsive">
                            <table class="table datatable">
                                <thead>
                                <tr>
                                    <th>{{__('رقم الفاتورة')}}</th>
                                    <th>{{ __('تاريخ') }}</th>
                                    <th>{{ __('عميل') }}</th>
                                    <th>{{ __('نوع الطلب') }}</th>
                                    <th>{{ __('مستودع') }}</th>
                                    <th>{{ __('المجموع') }}</th>
                                    <th>{{ __('حالة الدفع') }}</th>
                                    <th>{{ __('منشئ الفاتورة') }}</th>
                                    <th>{{ __('الإجراءات') }}</th>
                                </tr>
                                </thead>
                                <tbody>
                                @foreach ($posPayments as $pos)
                                    <tr class="{{ !empty($pos->customer) && $pos->customer->is_delivery ? 'table-warning' : '' }}">
                                        <td>
                                            <a href="{{ route('pos.show', \Crypt::encrypt($pos->id)) }}" class="btn btn-outline-primary">
                                                {{ Auth::user()->posNumberFormat($pos->pos_id) }}
                                            </a>
                                        </td>
                                        <td>{{ Auth::user()->dateFormat($pos->pos_date) }}</td>
                                        <td>{{ !empty($pos->customer) ? $pos->customer->name : __('Walk-in Customer') }}</td>
                                        <td>
                                            @if(!empty($pos->customer) && $pos->customer->is_delivery)
                                                <span class="badge bg-info text-white">
                                                    <i class="ti ti-truck-delivery"></i> {{ __('توصيل') }}
                                                </span>
                                            @else
                                                <span class="badge bg-secondary text-white">
                                                    <i class="ti ti-shopping-cart"></i> {{ __('عادي') }}
                                                </span>
                                            @endif
                                        </td>
                                        <td>{{ !empty($pos->warehouse) ? $pos->warehouse->name : '' }}</td>
                                        <td>{{ Auth::user()->priceFormat($pos->getTotal()) }}</td>
                                        <td>
                                            @if(!empty($pos->customer) && $pos->customer->is_delivery)
                                                @if($pos->is_payment_set)
                                                    <span class="badge bg-success text-white" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('تم التحصيل من مندوب التوصيل') }} ✅</span>
                                                @else
                                                    <span class="badge bg-warning text-dark" style="font-size: 0.85rem; padding: 0.35em 0.6em;">{{ __('جاري التحصيل') }} 🚚</span>
                                                @endif
                                            @else
                                                <span class="badge bg-primary text-white">{{ ucfirst($pos->payment_method) }}</span>
                                            @endif
                                        </td>
                                        <td>{{ !empty($pos->createdBy) ? $pos->createdBy->name : '' }}</td>
                                        <td class="Action">
                                            <span>
                                                <div class="action-btn bg-warning ms-2">
                                                    <a href="{{ route('pos.show', \Crypt::encrypt($pos->id)) }}" class="mx-3 btn btn-sm align-items-center" data-bs-toggle="tooltip" title="{{__('Show')}}" data-original-title="{{__('Detail')}}">
                                                        <i class="ti ti-eye text-white"></i>
                                                    </a>
                                                </div>
                                                
                                                {{-- زر تحصيل الدفعة للطلبات المعلقة --}}
                                                @if(!empty($pos->customer) && $pos->customer->is_delivery && !$pos->is_payment_set)
                                                    <div class="action-btn bg-success ms-2">
                                                        <a href="#" data-url="{{ route('pos.collect.payment') }}" 
                                                           data-ajax-popup="true" data-title="{{ __('تحصيل الدفعة') }}" 
                                                           class="mx-3 btn btn-sm align-items-center collect-payment-btn" 
                                                           data-pos-id="{{ $pos->id }}"
                                                           data-bs-toggle="tooltip" title="{{__('تحصيل الدفعة')}}" 
                                                           data-original-title="{{__('تحصيل الدفعة')}}">
                                                            <i class="ti ti-cash text-white"></i>
                                                        </a>
                                                    </div>
                                                @endif

                                                <div class="action-btn bg-primary ms-2">
                                                    <a href="{{ route('pos.pdf', \Crypt::encrypt($pos->id))}}" class="mx-3 btn btn-sm align-items-center" data-bs-toggle="tooltip" title="{{__('Print Invoice')}}" data-original-title="{{__('Print Invoice')}}" target="_blank">
                                                        <i class="ti ti-printer text-white"></i>
                                                    </a>
                                                </div>
                                            </span>
                                        </td>
                                    </tr>
                                @endforeach
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
