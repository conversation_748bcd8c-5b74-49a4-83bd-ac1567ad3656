<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Spatie\Permission\Models\Permission;
use Spatie\Permission\Models\Role;

class DeleveryPermissionSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $arrPermissions = [
            [
                "name" => "manage delevery",
                "guard_name" => "web",
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                "name" => "show delevery",
                "guard_name" => "web",
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                "name" => "create delevery",
                "guard_name" => "web",
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                "name" => "edit delevery",
                "guard_name" => "web",
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
            [
                "name" => "delete delevery",
                "guard_name" => "web",
                "created_at" => date('Y-m-d H:i:s'),
                "updated_at" => date('Y-m-d H:i:s'),
            ],
        ];

        // إضافة الصلاحيات فقط إذا لم تكن موجودة
        foreach ($arrPermissions as $permission) {
            Permission::firstOrCreate(
                ['name' => $permission['name'], 'guard_name' => $permission['guard_name']],
                $permission
            );
        }

        $companyRole = Role::updateOrCreate(
            ['name' => 'company'],
            [
                'name' => 'company',
                'created_by' => 0,
            ]
        );
        $companyPermissions = [
            "manage delevery",
            "show delevery",
            "create delevery",
            "edit delevery",
            "delete delevery",
        ];
        $companyRole->givePermissionTo($companyPermissions);

        // إضافة صلاحيات إضافية للمستخدمين الذين لديهم صلاحية delivery
        $deliveryRole = Role::where('name', 'Delivery')->first();
        if ($deliveryRole) {
            $deliveryRole->givePermissionTo([
                'show financial record',
                'manage invoice',
                'show invoice',
                'show pos'
            ]);
        }

        // إضافة صلاحيات إضافية لجميع المستخدمين الذين لديهم صلاحية manage delevery
        $usersWithDeliveryPermission = \App\Models\User::whereHas('permissions', function($query) {
            $query->where('name', 'manage delevery');
        })->orWhereHas('roles.permissions', function($query) {
            $query->where('name', 'manage delevery');
        })->get();

        foreach ($usersWithDeliveryPermission as $user) {
            $user->givePermissionTo([
                'show financial record',
                'manage invoice',
                'show invoice',
                'show pos'
            ]);
        }
    }
}
