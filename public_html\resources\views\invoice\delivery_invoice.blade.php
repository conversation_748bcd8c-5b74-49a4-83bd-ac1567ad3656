@extends('layouts.admin')
@section('page-title')
    {{ __('Invoice Detail') }}
@endsection
@push('script-page')
    <script>
        $(document).on('click', '#shipping', function () {
            var url = $(this).data('url');
            var is_display = $("#shipping").is(":checked");
            $.ajax({
                url: url,
                type: 'get',
                data: {
                    'is_display': is_display,
                },
                success: function (data) {
                }
            });
        })
    </script>
@endpush

@php
    $settings = Utility::settings();
@endphp
@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item"><a href="{{route('invoice.index')}}">{{__('Invoice')}}</a></li>
    <li class="breadcrumb-item">{{ AUth::user()->invoiceNumberFormat($invoice->invoice_id) }}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        <a href="{{ route('invoice.pdf', Crypt::encrypt($invoice->id))}}" class="btn btn-primary" target="_blank" >{{__('Download')}}</a>
    </div>
    
    {{-- زر الدفع الخاص بـ Delivery --}}
    @if($invoice->status != 0 && $invoice->getDue() > 0)
        <div class="float-end me-2">
            @if(Auth::user()->can('manage delevery'))
                <a href="#" data-url="{{ route('invoice.delivery.payment', $invoice->id) }}" 
                   data-ajax-popup="true" data-title="{{ __('Delivery Payment') }}" 
                   class="btn btn-success text-white">
                    {{__('PAY')}} 🚚
                </a>
            @endif
        </div>
    @endif
@endsection

@section('content')
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-body">
                    <div class="invoice">
                        <div class="invoice-print">
                            <div class="row invoice-title mt-2">
                                <div class="col-xs-12 col-sm-12 col-nd-6 col-lg-6 col-12">
                                    <h4>{{ __('Invoice') }}</h4>
                                </div>
                                <div class="col-xs-12 col-sm-12 col-nd-6 col-lg-6 col-12 text-end">
                                    <h4 class="invoice-number">
                                        {{ AUth::user()->invoiceNumberFormat($invoice->invoice_id) }}</h4>
                                </div>
                                <div class="col-12">
                                    <hr>
                                </div>
                            </div>
                            
                            {{-- معلومات الفاتورة --}}
                            <div class="row">
                                <div class="col text-end">
                                    <div class="d-flex align-items-center justify-content-end">
                                        <div class="me-4">
                                            <small>
                                                <strong>{{ __('Issue Date') }} :</strong><br>
                                                {{ \Auth::user()->dateFormat($invoice->issue_date) }}<br><br>
                                            </small>
                                        </div>
                                        <div>
                                            <small>
                                                <strong>{{ __('Due Date') }} :</strong><br>
                                                {{ \Auth::user()->dateFormat($invoice->due_date) }}<br><br>
                                            </small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            
                            {{-- معلومات العميل --}}
                            <div class="row">
                                <div class="col">
                                    <small class="font-style">
                                        <strong>{{ __('Billed To') }} :</strong><br>
                                        {{ !empty($customer->billing_name) ? $customer->billing_name : '' }}<br>
                                        {{ !empty($customer->billing_phone) ? $customer->billing_phone : '' }}<br>
                                        {{ !empty($customer->billing_address) ? $customer->billing_address : '' }}<br>
                                        {{ !empty($customer->billing_city) ? $customer->billing_city . ' ,' : '' }}
                                        {{ !empty($customer->billing_state) ? $customer->billing_state . ' ,' : '' }}
                                        {{ !empty($customer->billing_zip) ? $customer->billing_zip : '' }}<br>
                                        {{ !empty($customer->billing_country) ? $customer->billing_country : '' }}
                                    </small>
                                </div>
                                @if (($invoice->shipping_display == 1))
                                    <div class="col">
                                        <small>
                                            <strong>{{ __('Shipped To') }} :</strong><br>
                                            {{ !empty($customer->shipping_name) ? $customer->shipping_name : '' }}<br>
                                            {{ !empty($customer->shipping_phone) ? $customer->shipping_phone : '' }}<br>
                                            {{ !empty($customer->shipping_address) ? $customer->shipping_address : '' }}<br>
                                            {{ !empty($customer->shipping_city) ? $customer->shipping_city . ' ,' : '' }}
                                            {{ !empty($customer->shipping_state) ? $customer->shipping_state . ' ,' : '' }}
                                            {{ !empty($customer->shipping_zip) ? $customer->shipping_zip : '' }}<br>
                                            {{ !empty($customer->shipping_country) ? $customer->shipping_country : '' }}
                                        </small>
                                    </div>
                                @endif
                            </div>
                            
                            {{-- حالة الفاتورة للـ Delivery --}}
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="alert alert-info">
                                        <h5 class="mb-0">
                                            <i class="fas fa-truck"></i>
                                            {{ __('Delivery Status') }}:
                                            @if($invoice->status == 1)
                                                <span class="badge bg-warning">{{ __('جاري التحصيل') }} 🚚</span>
                                            @elseif($invoice->status == 3)
                                                <span class="badge bg-info">{{ __('Partially Paid') }}</span>
                                            @elseif($invoice->status == 4)
                                                <span class="badge bg-success">{{ __('Paid') }}</span>
                                            @else
                                                <span class="badge bg-secondary">{{ __('Draft') }}</span>
                                            @endif
                                        </h5>
                                        <p class="mb-0 mt-2">
                                            <strong>{{ __('Amount Due') }}:</strong>
                                            <span class="text-danger h5">{{ \Auth::user()->priceFormat($invoice->getDue()) }}</span>
                                        </p>
                                    </div>
                                </div>
                            </div>

                            {{-- جدول المنتجات --}}
                            <div class="row mt-3">
                                <div class="col-12">
                                    <div class="table-responsive">
                                        <table class="table table-striped">
                                            <thead>
                                                <tr>
                                                    <th>{{ __('Item') }}</th>
                                                    <th>{{ __('Quantity') }}</th>
                                                    <th>{{ __('Rate') }}</th>
                                                    <th>{{ __('Discount') }}</th>
                                                    <th>{{ __('Tax') }}</th>
                                                    <th>{{ __('Price') }}</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                @foreach ($iteams as $key => $iteam)
                                                    <tr>
                                                        <td>{{ !empty($iteam->product) ? $iteam->product->name : $iteam->product_name }}</td>
                                                        <td>{{ $iteam->quantity }}</td>
                                                        <td>{{ \Auth::user()->priceFormat($iteam->price) }}</td>
                                                        <td>{{ \Auth::user()->priceFormat($iteam->discount) }}</td>
                                                        <td>
                                                            @if (!empty($iteam->tax))
                                                                @php
                                                                    $totalTaxRate = 0;
                                                                    $taxes = \App\Models\Utility::tax($iteam->tax);
                                                                @endphp
                                                                @foreach ($taxes as $tax)
                                                                    @php
                                                                        $totalTaxRate += $tax->rate;
                                                                    @endphp
                                                                @endforeach
                                                                {{ $totalTaxRate }}%
                                                            @else
                                                                0%
                                                            @endif
                                                        </td>
                                                        <td>{{ \Auth::user()->priceFormat($iteam->price * $iteam->quantity - $iteam->discount + (\App\Models\Utility::taxRate($iteam->tax, $iteam->price, $iteam->quantity, $iteam->discount))) }}</td>
                                                    </tr>
                                                @endforeach
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <td colspan="5" class="text-end"><b>{{ __('Sub Total') }}</b></td>
                                                    <td class="text-end">{{ \Auth::user()->priceFormat($invoice->getSubTotal()) }}</td>
                                                </tr>
                                                @if ($invoice->getTotalDiscount())
                                                    <tr>
                                                        <td colspan="5" class="text-end"><b>{{ __('Discount') }}</b></td>
                                                        <td class="text-end">{{ \Auth::user()->priceFormat($invoice->getTotalDiscount()) }}</td>
                                                    </tr>
                                                @endif
                                                @if (!empty($invoice->getTotalTax()))
                                                    <tr>
                                                        <td colspan="5" class="text-end"><b>{{ __('Tax') }}</b></td>
                                                        <td class="text-end">{{ \Auth::user()->priceFormat($invoice->getTotalTax()) }}</td>
                                                    </tr>
                                                @endif
                                                <tr>
                                                    <td colspan="5" class="text-end"><b>{{ __('Total') }}</b></td>
                                                    <td class="text-end">{{ \Auth::user()->priceFormat($invoice->getTotal()) }}</td>
                                                </tr>
                                                <tr>
                                                    <td colspan="5" class="text-end"><b>{{ __('Paid') }}</b></td>
                                                    <td class="text-end">{{ \Auth::user()->priceFormat($invoice->getTotal() - $invoice->getDue()) }}</td>
                                                </tr>
                                                <tr>
                                                    <td colspan="5" class="text-end"><b>{{ __('Due') }}</b></td>
                                                    <td class="text-end text-danger">
                                                        <strong>{{ \Auth::user()->priceFormat($invoice->getDue()) }}</strong>
                                                    </td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
