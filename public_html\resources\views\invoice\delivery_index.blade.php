@extends('layouts.admin')
@section('page-title')
    {{ __('الفواتير المرسلة للتحصيل') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{route('dashboard')}}">{{__('Dashboard')}}</a></li>
    <li class="breadcrumb-item">{{__('الفواتير المرسلة للتحصيل')}}</li>
@endsection

@section('action-btn')
    <div class="float-end">
        <a href="#" data-bs-toggle="tooltip" title="{{__('View')}}" class="btn btn-sm btn-primary">
            <i class="ti ti-truck-delivery"></i> {{__('Delivery Dashboard')}}
        </a>
    </div>
@endsection

@section('content')
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-body table-border-style">
                    <div class="table-responsive">
                        <table class="table datatable">
                            <thead>
                                <tr>
                                    <th>{{__('Invoice')}}</th>
                                    <th>{{__('Customer')}}</th>
                                    <th>{{__('Issue Date')}}</th>
                                    <th>{{__('Due Date')}}</th>
                                    <th>{{__('Amount')}}</th>
                                    <th>{{__('Status')}}</th>
                                    <th>{{__('Action')}}</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach ($invoices as $invoice)
                                    <tr>
                                        <td class="Id">
                                            <a href="{{ route('invoice.show',\Crypt::encrypt($invoice->id)) }}" class="btn btn-outline-primary">
                                                {{ AUth::user()->invoiceNumberFormat($invoice->invoice_id) }}
                                            </a>
                                        </td>
                                        <td>{{ !empty($invoice->customer) ? $invoice->customer->name : '' }}</td>
                                        <td>{{ Auth::user()->dateFormat($invoice->issue_date) }}</td>
                                        <td>{{ Auth::user()->dateFormat($invoice->due_date) }}</td>
                                        <td>{{ Auth::user()->priceFormat($invoice->getTotal()) }}</td>
                                        <td>
                                            @if($invoice->status == 0)
                                                <span class="badge bg-secondary p-2 px-3 rounded">{{ __('Draft') }}</span>
                                            @elseif($invoice->status == 1)
                                                <span class="badge bg-warning p-2 px-3 rounded">{{ __('جاري التحصيل') }} 🚚</span>
                                            @elseif($invoice->status == 2)
                                                <span class="badge bg-danger p-2 px-3 rounded">{{ __('Unpaid') }}</span>
                                            @elseif($invoice->status == 3)
                                                <span class="badge bg-info p-2 px-3 rounded">{{ __('Partially Paid') }}</span>
                                            @elseif($invoice->status == 4)
                                                <span class="badge bg-success p-2 px-3 rounded">{{ __('Paid') }}</span>
                                            @endif
                                        </td>
                                        <td class="Action">
                                            <span>
                                                @can('show invoice')
                                                    <div class="action-btn bg-warning ms-2">
                                                        <a href="{{ route('invoice.show',\Crypt::encrypt($invoice->id)) }}" class="mx-3 btn btn-sm align-items-center" data-bs-toggle="tooltip" title="{{__('Show')}}" data-original-title="{{__('Detail')}}">
                                                            <i class="ti ti-eye text-white"></i>
                                                        </a>
                                                    </div>
                                                @endcan
                                                
                                                {{-- زر الدفع للـ Delivery --}}
                                                @if($invoice->status != 0 && $invoice->getDue() > 0)
                                                    <div class="action-btn bg-success ms-2">
                                                        <a href="#" data-url="{{ route('invoice.delivery.payment', $invoice->id) }}" 
                                                           data-ajax-popup="true" data-title="{{ __('Delivery Payment') }}" 
                                                           class="mx-3 btn btn-sm align-items-center" 
                                                           data-bs-toggle="tooltip" title="{{__('Collect Payment')}}" 
                                                           data-original-title="{{__('Collect Payment')}}">
                                                            <i class="ti ti-cash text-white"></i>
                                                        </a>
                                                    </div>
                                                @endif

                                                <div class="action-btn bg-primary ms-2">
                                                    <a href="{{ route('invoice.pdf', \Crypt::encrypt($invoice->id))}}" class="mx-3 btn btn-sm align-items-center" data-bs-toggle="tooltip" title="{{__('Print Invoice')}}" data-original-title="{{__('Print Invoice')}}" target="_blank">
                                                        <i class="ti ti-printer text-white"></i>
                                                    </a>
                                                </div>
                                            </span>
                                        </td>
                                    </tr>
                                @endforeach
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
